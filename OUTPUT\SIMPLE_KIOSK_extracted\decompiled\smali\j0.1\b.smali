.class public final Lj0/b;
.super Ljava/lang/Object;
.source "SourceFile"

# interfaces
.implements Lv0/a;
.implements Lz0/n;
.implements Lw0/a;


# instance fields
.field public e:LD0/j;

.field public f:LD0/j;

.field public g:Lp0/d;

.field public h:LF0/N;


# direct methods
.method public constructor <init>()V
    .locals 0

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 2
    .line 3
    .line 4
    return-void
.end method


# virtual methods
.method public final a()Ljava/lang/Boolean;
    .locals 3

    .line 1
    sget-object v0, Ljava/lang/Bo<PERSON>an;->TRUE:Ljava/lang/Boolean;

    .line 39
    .line 40
    return-object v0
.end method

.method public final b(Lj/o;)V
    .locals 1

    .line 1
    const-string v0, "binding"

    .line 2
    .line 3
    invoke-static {p1, v0}, LS0/h;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p1, Lj/o;->a:L<PERSON>va/lang/Object;

    .line 7
    .line 8
    check-cast p1, Lp0/d;

    .line 9
    .line 10
    iput-object p1, p0, Lj0/b;->g:Lp0/d;

    .line 11
    .line 12
    return-void
.end method

.method public final c(Lj/o;)V
    .locals 1

    .line 1
    const-string v0, "binding"

    invoke-static {p1, v0}, LS0/h;->e(Ljava/lang/Object;Ljava/lang/String;)V

    return-void
.end method

.method public final d()V
    .locals 1

    .line 1
    const/4 v0, 0x0

    .line 2
    iput-object v0, p0, Lj0/b;->g:Lp0/d;

    .line 3
    .line 4
    return-void
.end method

.method public final f(LB0/c;Ly0/j;)V
    .locals 3

    .line 1
    const-string v0, "call"

    .line 2
    .line 3
    invoke-static {p1, v0}, LS0/h;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p1, LB0/c;->f:Ljava/lang/Object;

    .line 7
    .line 8
    check-cast p1, Ljava/lang/String;

    .line 9
    .line 10
    if-eqz p1, :cond_9

    .line 11
    .line 12
    invoke-virtual {p1}, Ljava/lang/String;->hashCode()I

    .line 13
    .line 14
    .line 15
    move-result v0

    .line 16
    const/4 v1, 0x0

    .line 17
    const/4 v2, 0x0

    .line 18
    sparse-switch v0, :sswitch_data_0

    .line 19
    .line 20
    .line 21
    goto/16 :goto_2

    .line 22
    .line 23
    :sswitch_0
    const-string v0, "isInKioskMode"

    .line 24
    .line 25
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 26
    .line 27
    .line 28
    move-result p1

    .line 29
    if-nez p1, :cond_0

    .line 30
    .line 31
    goto/16 :goto_2

    .line 32
    .line 33
    :cond_0
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 34
    .line 35
    .line 36
    .line 37
    invoke-virtual {p2, p1}, Ly0/j;->c(Ljava/lang/Object;)V

    .line 38
    .line 39
    .line 40
    goto/16 :goto_3

    .line 41
    .line 42
    :sswitch_1
    const-string v0, "startKioskMode"

    .line 43
    .line 44
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 45
    .line 46
    .line 47
    move-result p1

    .line 48
    if-nez p1, :cond_1

    .line 49
    .line 50
    goto/16 :goto_2

    .line 51
    .line 52
    :cond_1
    # Check if user wants kiosk mode enabled
    invoke-direct {p0}, Lj0/b;->shouldEnableKiosk()Z
    move-result v0

    if-eqz v0, :cond_1_skip

    # User wants kiosk mode - start lock task
    iget-object v0, p0, Lj0/b;->g:Lp0/d;
    if-eqz v0, :cond_1_skip
    invoke-virtual {v0}, Landroid/app/Activity;->startLockTask()V

    :cond_1_skip
    sget-object p1, Ljava/lang/Boolean;->TRUE:Ljava/lang/Boolean;

    .line 80
    .line 81
    invoke-virtual {p2, p1}, Ly0/j;->c(Ljava/lang/Object;)V

    .line 82
    .line 83
    .line 84
    goto :goto_3

    .line 85
    :sswitch_2
    const-string v0, "isManagedKiosk"

    .line 86
    .line 87
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 88
    .line 89
    .line 90
    move-result p1

    .line 91
    if-nez p1, :cond_2

    .line 92
    .line 93
    goto :goto_2

    .line 94
    :cond_2
    iget-object p1, p0, Lj0/b;->g:Lp0/d;

    .line 95
    .line 96
    if-eqz p1, :cond_3

    .line 97
    .line 98
    const-string v0, "activity"

    .line 99
    .line 100
    invoke-virtual {p1, v0}, Landroid/app/Activity;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    .line 101
    .line 102
    .line 103
    move-result-object p1

    .line 104
    goto :goto_0

    .line 105
    :cond_3
    move-object p1, v1

    .line 106
    :goto_0
    instance-of v0, p1, Landroid/app/ActivityManager;

    .line 107
    .line 108
    if-eqz v0, :cond_4

    .line 109
    .line 110
    check-cast p1, Landroid/app/ActivityManager;

    .line 111
    .line 112
    goto :goto_1

    .line 113
    :cond_4
    move-object p1, v1

    .line 114
    :goto_1
    if-nez p1, :cond_5

    .line 115
    .line 116
    invoke-virtual {p2, v1}, Ly0/j;->c(Ljava/lang/Object;)V

    .line 117
    .line 118
    .line 119
    goto :goto_3

    .line 120
    :cond_5
    sget-object p1, Ljava/lang/Boolean;->FALSE:Ljava/lang/Boolean;

    .line 121
    .line 122
    .line 123
    .line 124
    .line 125
    .line 126
    .line 127
    .line 128
    .line 129
    .line 130
    .line 131
    .line 132
    invoke-virtual {p2, p1}, Ly0/j;->c(Ljava/lang/Object;)V

    .line 133
    .line 134
    .line 135
    goto :goto_3

    .line 136
    :sswitch_3
    const-string v0, "stopKioskMode"

    .line 137
    .line 138
    invoke-virtual {p1, v0}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    .line 139
    .line 140
    .line 141
    move-result p1

    .line 142
    if-nez p1, :cond_6

    .line 143
    .line 144
    goto :goto_2

    .line 145
    :cond_6
    iget-object p1, p0, Lj0/b;->g:Lp0/d;

    .line 146
    .line 147
    if-eqz p1, :cond_7

    .line 148
    .line 149
    invoke-virtual {p1}, Landroid/app/Activity;->stopLockTask()V

    .line 150
    .line 151
    .line 152
    :cond_7
    invoke-virtual {p2, v1}, Ly0/j;->c(Ljava/lang/Object;)V

    .line 153
    .line 154
    .line 155
    iget-object p1, p0, Lj0/b;->h:LF0/N;

    .line 156
    .line 157
    if-eqz p1, :cond_8

    .line 158
    .line 159
    invoke-virtual {p1}, LF0/N;->b()V

    .line 160
    .line 161
    .line 162
    goto :goto_3

    .line 163
    :cond_8
    const-string p1, "kioskModeHandler"

    .line 164
    .line 165
    invoke-static {p1}, LS0/h;->g(Ljava/lang/String;)V

    .line 166
    .line 167
    .line 168
    throw v1

    .line 169
    :cond_9
    :goto_2
    invoke-virtual {p2}, Ly0/j;->b()V

    .line 170
    .line 171
    .line 172
    :goto_3
    return-void

    nop

    .line 173
    :sswitch_data_0
    .sparse-switch
        -0x29d8b7b6 -> :sswitch_3
        0x2cc1ad4 -> :sswitch_2
        0x38f077ea -> :sswitch_1
        0x7ad1f6fd -> :sswitch_0
    .end sparse-switch
.end method

.method public final g()V
    .locals 0

    .line 1
    return-void
.end method

.method private shouldEnableKiosk()Z
    .locals 4

    # Get activity context
    iget-object v0, p0, Lj0/b;->g:Lp0/d;
    if-nez v0, :cond_0

    # No activity context - default to false (kiosk disabled)
    const/4 v0, 0x0
    return v0

    :cond_0
    # Get SharedPreferences
    const-string v1, "kiosk_control"
    const/4 v2, 0x0
    invoke-virtual {v0, v1, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;
    move-result-object v0

    # Get kiosk mode state (default false = kiosk disabled)
    const-string v1, "kiosk_enabled"
    invoke-interface {v0, v1, v2}, Landroid/content/SharedPreferences;->getBoolean(Ljava/lang/String;Z)Z
    move-result v0

    return v0
.end method

.method public toggleKioskMode()V
    .locals 5

    # Get activity context
    iget-object v0, p0, Lj0/b;->g:Lp0/d;
    if-nez v0, :cond_0
    return-void

    :cond_0
    # Get current state
    invoke-direct {p0}, Lj0/b;->shouldEnableKiosk()Z
    move-result v1

    # Toggle state
    xor-int/lit8 v1, v1, 0x1

    # Save new state to SharedPreferences
    const-string v2, "kiosk_control"
    const/4 v3, 0x0
    invoke-virtual {v0, v2, v3}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;
    move-result-object v2

    invoke-interface {v2}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;
    move-result-object v2
    const-string v3, "kiosk_enabled"
    invoke-interface {v2, v3, v1}, Landroid/content/SharedPreferences$Editor;->putBoolean(Ljava/lang/String;Z)Landroid/content/SharedPreferences$Editor;
    invoke-interface {v2}, Landroid/content/SharedPreferences$Editor;->apply()V

    # Show toast and apply immediately
    if-eqz v1, :cond_1
    const-string v2, "🔒 KIOSK MODE AKTIF - Tekan Volume Up 5x untuk nonaktif"
    invoke-virtual {v0}, Landroid/app/Activity;->startLockTask()V
    goto :cond_2

    :cond_1
    const-string v2, "🔓 KIOSK MODE NONAKTIF - Tekan Volume Up 5x untuk aktif"
    invoke-virtual {v0}, Landroid/app/Activity;->stopLockTask()V

    :cond_2
    const/4 v3, 0x1
    invoke-static {v0, v2, v3}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;
    move-result-object v0
    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    return-void
.end method



.method private shouldEnableKiosk()Z
    .locals 4

    # Get activity context
    iget-object v0, p0, Lj0/b;->g:Lp0/d;
    if-nez v0, :cond_0

    # No activity context - default to false (bypass kiosk)
    const/4 v0, 0x0
    return v0

    :cond_0
    # Get SharedPreferences
    const-string v1, "kiosk_control"
    const/4 v2, 0x0
    invoke-virtual {v0, v1, v2}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;
    move-result-object v0

    # Get kiosk mode state (default false = bypass)
    const-string v1, "enable_kiosk"
    invoke-interface {v0, v1, v2}, Landroid/content/SharedPreferences;->getBoolean(Ljava/lang/String;Z)Z
    move-result v0

    return v0
.end method

.method public toggleKioskControl()V
    .locals 5

    # Get activity context
    iget-object v0, p0, Lj0/b;->g:Lp0/d;
    if-nez v0, :cond_0
    return-void

    :cond_0
    # Get current state
    invoke-direct {p0}, Lj0/b;->shouldEnableKiosk()Z
    move-result v1

    # Toggle state
    xor-int/lit8 v1, v1, 0x1

    # Save new state to SharedPreferences
    const-string v2, "kiosk_control"
    const/4 v3, 0x0
    invoke-virtual {v0, v2, v3}, Landroid/content/Context;->getSharedPreferences(Ljava/lang/String;I)Landroid/content/SharedPreferences;
    move-result-object v2

    invoke-interface {v2}, Landroid/content/SharedPreferences;->edit()Landroid/content/SharedPreferences$Editor;
    move-result-object v2
    const-string v3, "enable_kiosk"
    invoke-interface {v2, v3, v1}, Landroid/content/SharedPreferences$Editor;->putBoolean(Ljava/lang/String;Z)Landroid/content/SharedPreferences$Editor;
    invoke-interface {v2}, Landroid/content/SharedPreferences$Editor;->apply()V

    # Show toast and apply immediately
    if-eqz v1, :cond_1
    const-string v2, "🔒 KIOSK MODE AKTIF"
    invoke-virtual {v0}, Landroid/app/Activity;->startLockTask()V
    goto :cond_2

    :cond_1
    const-string v2, "🔓 KIOSK MODE NONAKTIF"
    invoke-virtual {v0}, Landroid/app/Activity;->stopLockTask()V

    :cond_2
    const/4 v3, 0x1
    invoke-static {v0, v2, v3}, Landroid/widget/Toast;->makeText(Landroid/content/Context;Ljava/lang/CharSequence;I)Landroid/widget/Toast;
    move-result-object v0
    invoke-virtual {v0}, Landroid/widget/Toast;->show()V

    return-void
.end method

.method public final h(LI/m;)V
    .locals 9

    .line 1
    const-string v0, "flutterPluginBinding"

    .line 2
    .line 3
    invoke-static {p1, v0}, LS0/h;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    new-instance v0, LD0/j;

    .line 7
    .line 8
    iget-object p1, p1, LI/m;->b:Ljava/lang/Object;

    .line 9
    .line 10
    check-cast p1, Lz0/f;

    .line 11
    .line 12
    const-string v1, "com.mews.kiosk_mode/kiosk_mode"

    .line 13
    .line 14
    const/16 v2, 0xf

    .line 15
    .line 16
    invoke-direct {v0, p1, v1, v2}, LD0/j;-><init>(Lz0/f;Ljava/lang/String;I)V

    .line 17
    .line 18
    .line 19
    iput-object v0, p0, Lj0/b;->e:LD0/j;

    .line 20
    .line 21
    invoke-virtual {v0, p0}, LD0/j;->Q(Lz0/n;)V

    .line 22
    .line 23
    .line 24
    new-instance v0, LF0/N;

    .line 25
    .line 26
    new-instance v8, Lj0/a;

    .line 27
    .line 28
    const-class v4, Lj0/b;

    .line 29
    .line 30
    const-string v5, "isInKioskMode"

    .line 31
    .line 32
    const/4 v2, 0x0

    .line 33
    const-string v6, "isInKioskMode()Ljava/lang/Boolean;"

    .line 34
    .line 35
    const/4 v7, 0x0

    .line 36
    move-object v1, v8

    .line 37
    move-object v3, p0

    .line 38
    invoke-direct/range {v1 .. v7}, LS0/g;-><init>(ILjava/lang/Object;Ljava/lang/Class;Ljava/lang/String;Ljava/lang/String;I)V

    .line 39
    .line 40
    .line 41
    invoke-direct {v0, v8}, LF0/N;-><init>(Lj0/a;)V

    .line 42
    .line 43
    .line 44
    iput-object v0, p0, Lj0/b;->h:LF0/N;

    .line 45
    .line 46
    new-instance v0, LD0/j;

    .line 47
    .line 48
    const-string v1, "com.mews.kiosk_mode/kiosk_mode_stream"

    .line 49
    .line 50
    const/16 v2, 0xe

    .line 51
    .line 52
    invoke-direct {v0, p1, v1, v2}, LD0/j;-><init>(Lz0/f;Ljava/lang/String;I)V

    .line 53
    .line 54
    .line 55
    iput-object v0, p0, Lj0/b;->f:LD0/j;

    .line 56
    .line 57
    iget-object p1, p0, Lj0/b;->h:LF0/N;

    .line 58
    .line 59
    if-eqz p1, :cond_0

    .line 60
    .line 61
    invoke-virtual {v0, p1}, LD0/j;->R(Lz0/h;)V

    .line 62
    .line 63
    .line 64
    return-void

    .line 65
    :cond_0
    const-string p1, "kioskModeHandler"

    .line 66
    .line 67
    invoke-static {p1}, LS0/h;->g(Ljava/lang/String;)V

    .line 68
    .line 69
    .line 70
    const/4 p1, 0x0

    .line 71
    throw p1
.end method

.method public final i(LI/m;)V
    .locals 1

    .line 1
    const-string v0, "binding"

    .line 2
    .line 3
    invoke-static {p1, v0}, LS0/h;->e(Ljava/lang/Object;Ljava/lang/String;)V

    .line 4
    .line 5
    .line 6
    iget-object p1, p0, Lj0/b;->e:LD0/j;

    .line 7
    .line 8
    const/4 v0, 0x0

    .line 9
    if-eqz p1, :cond_1

    .line 10
    .line 11
    invoke-virtual {p1, v0}, LD0/j;->Q(Lz0/n;)V

    .line 12
    .line 13
    .line 14
    iget-object p1, p0, Lj0/b;->f:LD0/j;

    .line 15
    .line 16
    if-eqz p1, :cond_0

    .line 17
    .line 18
    invoke-virtual {p1, v0}, LD0/j;->R(Lz0/h;)V

    .line 19
    .line 20
    .line 21
    return-void

    .line 22
    :cond_0
    const-string p1, "eventChannel"

    .line 23
    .line 24
    invoke-static {p1}, LS0/h;->g(Ljava/lang/String;)V

    .line 25
    .line 26
    .line 27
    throw v0

    .line 28
    :cond_1
    const-string p1, "channel"

    .line 29
    .line 30
    invoke-static {p1}, LS0/h;->g(Ljava/lang/String;)V

    .line 31
    .line 32
    .line 33
    throw v0
.end method
